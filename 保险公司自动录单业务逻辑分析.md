# 保险公司自动录单业务逻辑分析

## 目录
- [1. 系统概述](#1-系统概述)
- [2. 保险公司业务差异](#2-保险公司业务差异)
- [3. 保单类型处理](#3-保单类型处理)
- [4. 特殊业务规则](#4-特殊业务规则)
- [5. 数据处理规则](#5-数据处理规则)
- [6. 异常处理机制](#6-异常处理机制)

## 1. 系统概述

### 1.1 核心功能
自动录单系统负责将保单数据转换为各保险公司要求的格式，并调用对应的录单接口。系统需要处理7家不同保险公司的业务差异。

### 1.2 支持的保险公司
| 公司代码 | 公司名称 | 主要特点 |
|---------|---------|---------|
| PICC | 人保财险 | 复杂货物分类、强制保费计算 |
| CPIC | 太保财险 | 特殊地址格式、固定开票信息 |
| PINGAN | 平安财险 | 新旧系统兼容、制裁条款、特殊分公司处理 |
| SINOSIG | 阳光财险 | 货物类别双重信息、固定附加险代码 |
| HUATAI | 华泰财险 | 简化配置、附加险合并 |
| TPIC | 太平财险 | 复杂JSON配置、运输方式细分 |
| DIC | 东海财险 | 最简配置 |

### 1.3 保单类型
- **国内险种**: 国内运输货物保险
- **国际险种**: 国际运输货物保险  
- **跨境电商**: 跨境电商货物保险
- **单车责任险**: 承运人责任保险

## 2. 保险公司业务差异

### 2.1 人保财险 (PICC)

#### 配置要求
- **基础信息**: 账号、密码、保单号头、可选方案代码
- **货物分类**: 需要大类+小类的二级分类映射
- **条款处理**: 国内险种支持主险+盗抢险组合

#### 特殊规则
- **货物小类映射**: 8个大类对应不同的小类代码
- **保费计算**: 强制使用保司费率，不支持保费同步功能
- **条款获取**: 国内险种使用产品条款，国际险种使用保单条款

#### 业务限制
- 系统无法手动录入保费，必须按保司最低保费计算
- 盗抢险不附加时填写0

### 2.2 太保财险 (CPIC)

#### 配置要求
- **基础信息**: 账号、密码、协议ID
- **货物分类**: 17类详细的货物小类映射

#### 特殊规则
- **地址格式**: 保持冒号格式，不转换为横杠
- **开票信息**: 个人开票时使用固定税号 `91310230MA1K2FMP33`
- **配置简洁**: 相对最简单的配置结构

### 2.3 平安财险 (PINGAN)

#### 配置要求
- **基础信息**: 账号、密码、协议、方案、险种代码
- **版本兼容**: 支持新旧两套系统，通过 `is_new` 标识区分

#### 特殊规则
- **新版本**: 使用方案代码和险种代码
- **旧版本**: 使用中文方案名和条款中文名
- **进出口处理**: 协议和方案支持用 `|` 分隔的出口/进口配置
- **制裁条款**: 自动检测制裁国家并添加制裁条款 `PL0900090`
- **倒签处理**: 深圳(20)、广东(29)分公司有特殊倒签保函处理

#### 条款映射
- 15种不同的条款代码对应中文名称
- 包含国内险种和国际条款

#### 货物分类
- 19个货物大类对应不同的小类代码

### 2.4 阳光财险 (SINOSIG)

#### 配置要求
- **基础信息**: 协议、方案、运输方式代码、货物类别(代码+名称)、主险代码

#### 特殊规则
- **货物类别**: 同时需要代码和名称两个字段
- **运输方式**: 直接使用运输方式代码
- **附加险**: 盗抢险使用固定代码 `DQX`

### 2.5 华泰财险 (HUATAI)

#### 配置要求
- **基础信息**: 账号、协议、方案、险种代码

#### 特殊规则
- **附加险**: 多个附加险用逗号分隔
- **配置简洁**: 相对简化的配置结构

### 2.6 太平财险 (TPIC)

#### 配置要求
- **基础信息**: 账号、密码、协议(复杂JSON结构)

#### 特殊规则
- **协议配置**: 使用复杂的JSON结构
  - 国内险种: 按运输方式分类
  - 国际险种: 按进出口+运输方式分类
- **错误处理**: 配置缺失时抛出异常
- **地址格式**: 使用冒号分隔格式

#### JSON配置结构
```
国内: {"运输方式代码": "协议号-描述"}
国际: {
  "EXPORT": {"运输方式代码": "协议号-描述"},
  "IMPORT": {"运输方式代码": "协议号-描述"}
}
```

### 2.7 东海财险 (DIC)

#### 配置要求
- **基础信息**: 方案、货物类型(代码-名称格式)

#### 特殊规则
- **配置最简**: 只需要2个参数
- **货物格式**: 使用 "代码-名称" 的组合格式

## 3. 保单类型处理

### 3.1 处理方法分配
| 保单类型 | 处理方法 | 说明 |
|---------|---------|------|
| 国内险种 | arrangeField | 国内运输货物保险 |
| 国际险种 | arrangeFieldIntl | 国际运输货物保险 |
| 跨境电商 | arrangeFieldIntl | 使用国际险种处理逻辑 |
| 单车责任险 | arrangeFieldLbt | 承运人责任保险 |

### 3.2 条款获取规则
- **国内险种**: 使用产品条款
- **国际/跨境**: 使用保单条款
- **条款分类**: 
  - `parent_id == '-1'` 为主险
  - 其他为附加险

### 3.3 字段差异
| 字段类型 | 国内险种 | 国际险种 | 单车责任险 |
|---------|---------|---------|-----------|
| 包装方式 | pack | packType | pack |
| 裸装条款 | 中文描述 | 英文描述 | 中文描述 |
| 投保类型 | 无 | export/import/overseas | 无 |
| 币种处理 | 人民币 | 多币种+汇率 | 人民币 |

## 4. 特殊业务规则

### 4.1 预设数据处理
- **触发条件**: `is_use_insure_preset_data = 1`
- **覆盖范围**: 投保人和被保人的所有信息
- **覆盖逻辑**: 预设数据不为空时覆盖原数据

### 4.2 地址格式处理
- **太保**: 保持冒号格式 `起运地:详细地址`
- **其他保司**: 转换为横杠格式 `起运地-详细地址`

### 4.3 保费计算规则
- **人保**: 强制使用保司费率，不支持保费同步
- **其他保司**: 支持保费同步功能
- **计算公式**: `保费 = max(保额 × 费率, 最低保费)`

### 4.4 制裁国家处理
- **适用保司**: 仅平安财险
- **检测逻辑**: 检查起运地或目的地是否在制裁名单中
- **处理方式**: 自动添加制裁条款 `PL0900090`

### 4.5 倒签处理
- **判断标准**: 提交时间 > 起运时间 + 允许倒签天数
- **保函处理**: 上传的倒签保函文件转为base64
- **特殊处理**: 平安深圳、广东分公司有默认倒签保函

### 4.6 保障到港处理
- **触发条件**: 起运地或目的地在保障到港地区列表中
- **例外情况**: 用户配置了免港到港条款时不生效
- **地址处理**: 根据保单类型和保司获取对应的地区代码

## 5. 数据处理规则

### 5.1 特殊字符处理
- **国际险种**: 清理换行符、制表符等特殊字符
- **处理范围**: 保单和货物信息的所有字段

### 5.2 币种处理
- **标准币种**: HKD、CNY、USD 直接处理
- **非标准币种**: 需要转换为USD并计算汇率
- **保费币种**: 支持按发票币种配置

### 5.3 开票信息处理
- **企业开票**: 使用发票抬头的公司名称和税号
- **个人开票**: 使用用户姓名和身份证号
- **太保特殊**: 个人开票时使用固定税号

### 5.4 投保类型映射
- `1` → `export` (出口)
- `2` → `import` (进口)  
- `3` → `overseas` (境外)

## 6. 异常处理机制

### 6.1 环境检查
- **本地环境**: 跳过录单，直接返回
- **生产环境**: 正常执行录单流程

### 6.2 配置验证
- **太平财险**: 检查运输方式对应的协议配置是否存在
- **缺失处理**: 抛出400异常，提示联系管理员

### 6.3 录单失败处理
- **检测方式**: 返回结果包含 "error" 字符串
- **通知机制**: 
  - 企业微信通知：发送流水号和错误信息
  - 邮件通知：内部邮箱 + 保司邮箱

### 6.4 通知配置
- **内部邮箱**: 固定的运营邮箱列表
- **保司邮箱**: 从产品配置中获取，用分号分隔
- **通知内容**: 流水号、错误提示、检查建议

### 6.5 系统接口
- **退回功能**: 支持将录单数据退回自动录单系统
- **重提检查**: 支持检查保单是否可以重新提交
- **环境隔离**: 本地环境返回模拟成功结果

## 总结

### 业务复杂度分析
1. **平安财险**: 最复杂，需要处理新旧系统、制裁条款、特殊分公司
2. **太平财险**: 配置复杂，JSON结构按运输方式和进出口分类
3. **人保财险**: 货物分类复杂，保费计算特殊
4. **太保财险**: 地址格式和开票信息特殊
5. **阳光财险**: 货物信息需要双重字段
6. **华泰财险**: 相对简单，主要是附加险处理
7. **东海财险**: 最简单的配置

### 重构关键点
1. **按保司拆分**: 每家保司的特殊逻辑独立处理
2. **配置外置**: 将硬编码的映射关系移到配置文件
3. **统一接口**: 定义标准的保司处理接口
4. **异常规范**: 统一的异常处理和错误码
5. **测试覆盖**: 每家保司的特殊逻辑都需要测试用例
