<?php

namespace App\Http\Controllers;

use App\Exports\FinancesExport;
use App\Http\Resources\PremiumReceivable\PremiumReceivablesResource;
use App\Models\PremiumReceivable;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class PremiumReceivableController extends Controller
{
    /**
     * 保费应收列表
     *
     * @param PremiumReceivable $premiumReceivable
     * @return PremiumReceivablesResource
     */
    public function index(PremiumReceivable $premiumReceivable)
    {
        $ids = request()->input('ids');
        $data = $premiumReceivable->getPremiumReceivables($pageable = true, $ids);

        return new PremiumReceivablesResource($data);
    }

    /**
     * 保费应收数据导出
     *
     * @param PremiumReceivable $premiumReceivable
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(PremiumReceivable $premiumReceivable)
    {
        $billId = request()->input('bill_id');

        $data = $premiumReceivable->getPremiumReceivables($pageable = false, $ids = null, $billId);

        return Excel::download(new FinancesExport($data), '保费应收处理数据导出-' . date('Y-m-d H:i:s') . '.xlsx');
    }
}
