<?php

namespace App\Support;

use App\Libraries\CaptureException\WeCom;
use App\Models\Policy;
use App\Models\UserSubjectClause;
use App\Notifications\PolicyInsureSubmitFailNotifiication;
use Exception;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use Str;

class AutoInsure
{
    use PrepareSettings;

    private $api_url = 'http://post.51baoya.com/api';              // 自动录单接口
    /* private $messages;                                                      // 保单数据，JSON格式
    private $settings;                                                      // 产品参数，JSON格式
    private $product_code;                                                  // 产品代码
    private $notifies;                                                      // 回调通知地址，JSON格式 */

    public function autopost($policy, $policyCargo, $product, $insureType = 'CARGO_CHN')
    {
        // 本地不走自动录单
        if (app()->environment('local', 'development', 'staging', 'testing')) {
            return;
        }

        switch ($policy['type']) {
            case Policy::TYPE_DOMESTIC:
                $data = $this->arrangeField($policy, $policyCargo);
                break;
            case Policy::TYPE_INTL:
            case Policy::TYPE_CBEC:
                $data = $this->arrangeFieldIntl($policy, $policyCargo);
                break;
            case Policy::TYPE_LIABILITY:
                $data = $this->arrangeFieldLbt($policy, $policyCargo);
                break;
            default:
                $data = [];
                break;
        }

        $data = $this->getInvoiceData($data, $policy, $policy['user']);
        $data['departureDate'] = strtotime($data['departureDate']);
        $messages = json_encode($data);
        $URL = config('app.url') . '/dashboard/api/v1/api/notify';
        $notifyUrl = ['order_no' => $policy['order_no'], 'url' => $URL];
        if ('' == $policy['notify_url']) {
            $notifies = [];
            array_push($notifies, $notifyUrl);
        } else {
            $notifies = [];
            array_push($notifies, $notifyUrl);
            if (null != json_decode($policy['notify_url'], true)) {
                foreach (json_decode($policy['notify_url'], true) as $value) {
                    array_push($notifies, $value);
                }
            } else {
                array_push($notifies, ['order_no' => $policy['trade_order_no'], 'url' => $policy['notify_url']]);
            }
        }

        $deductible = $product['additional']['deductible'];
        $special = $product['additional']['special_agreement'];

        if ($policy['policyCargo']['with_user_special_and_deductible'] == 1) {
            $deductible = $policy['policyCargo']['deductible'];
            $special = $policy['policyCargo']['special'];
        }

        $notifies = json_encode($notifies);
        $settings = json_encode([
            $this->getSetting($policy, $policyCargo, $product),
            $deductible,
            $special,
        ]);

        $callback = $this->send($messages, $settings, $product['additional']['config']['product_code'], $notifies, $policy);

        if (Str::contains($callback, 'error')) {
            $this->sendWeChatMessage($policy['order_no']);
            $this->sendMailMessage($policy);
        }

        return $callback;
    }

    public function send($messages, $settings, $product_code, $notifies, $policy)
    {
        $data = [
            'token' => $this->getAcessToken(),
            'message' => $messages,
            'settings' => $settings,
            'product_code' => $product_code,
            'notifies' => $notifies,
        ];

        $callback = $this->httpClient($this->api_url . '/create', $data, 'POST');

        return $callback;
    }

    /**
     * 获取开票信息
     *
     * @param $data
     * @param $policy
     * @param $user
     * @return mixed
     */
    protected function getInvoiceData($data, $policy, $user)
    {
        if (!is_null($user['invoice'])) {
            $data['invHead'] = $user['invoice']['company_name'];
            $data['invTaxNo'] = $user['invoice']['tax_no'];
        } else {
            $data['invHead'] = $user['name'];
            $data['invTaxNo'] = $user['idcard_no'];
            if ($policy['company']['identifier'] == 'CPIC') {
                $data['invTaxNo'] = '91310230MA1K2FMP33';
            }
        }

        return $data;
    }

    /**
     * 重新获取accesstoken.
     */
    private function getAcessToken()
    {
        $data['app_id'] = config('autoins.app_id');
        $data['secret_key'] = config('autoins.secret_key');

        $callback = $this->httpClient($this->api_url . '/login', $data);
        $callback = json_decode($callback, true);

        return $callback['token'];
    }

    private function httpClient($url, $params, $method = 'GET', $header = [], $multi = false)
    {
        $opts = [
            CURLOPT_TIMEOUT => 30,
            CURLOPT_RETURNTRANSFER => 1,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_HTTPHEADER => $header,
        ];

        /* 根据请求类型设置特定参数 */
        switch (strtoupper($method)) {
            case 'GET':
                $opts[CURLOPT_URL] = $url . '?' . http_build_query($params);
                break;
            case 'POST':
                //判断是否传输文件
                //$params = $multi ? $params : http_build_query($params);
                $opts[CURLOPT_URL] = $url;
                $opts[CURLOPT_POST] = 1;
                $opts[CURLOPT_POSTFIELDS] = $params;
                break;
            default:
                throw new Exception('不支持的请求方式！');
        }

        /* 初始化并执行curl请求 */
        $ch = curl_init();
        curl_setopt_array($ch, $opts);
        $data = curl_exec($ch);
        $error = curl_error($ch);
        curl_close($ch);
        if ($error) {
            throw new Exception('请求发生错误：' . $error);
        }

        return $data;
    }

    /**
     * 获取投保类型
     *
     * @param $insureType
     * @return mixed
     */
    protected function getInsureType($insureType)
    {
        $types = [
            '1' => 'export',
            '2' => 'import',
            '3' => 'overseas',
        ];

        return $types[$insureType];
    }

    /**
     * 获取保费
     *
     * @param int $coverage
     * @param float $rate
     * @param int $minimum
     * @return int
     */
    protected function calcPremium(int $coverage, float $rate, int $minimum = 0)
    {
        $premium = bcmul($coverage, bcdiv($rate, 10000, 6), 5);

        return round($premium < $minimum ? $minimum : $premium, 2);
    }

    /**
     * 是否为保障到港国家.
     *
     * @param $policy
     * @return bool
     */
    protected function isOnlyPortRegions($policy)
    {
        $onlyPortRegions = explode('|', $policy['product']['additional']['only_port_regions']);
        $departure = in_array($policy['type'], [Policy::TYPE_INTL, Policy::TYPE_CBEC])
            ? ($policy['company']['identifier'] == 'TPIC' ? explode(':', $policy['policyCargo']['departure'])[0] : $policy['policyCargo']['departure'])
            : explode('-', $policy['policyCargo']['departure'])[0];
        $destination = in_array($policy['type'], [Policy::TYPE_INTL, Policy::TYPE_CBEC])
            ? ($policy['company']['identifier'] == 'TPIC' ? explode(':', $policy['policyCargo']['destination'])[0] : $policy['policyCargo']['destination'])
            : explode('-', $policy['policyCargo']['destination'])[0];

        if (in_array($departure, $onlyPortRegions) || in_array($destination, $onlyPortRegions)) {
            $subjectClause = UserSubjectClause::where('user_id', $policy['user_id'])
                ->where('subject_id', $policy['policyCargo']['subject_id'])
                ->whereHas('companyBranches', fn($q) => $q->where('company_branch_id', $policy['company_branch_id']))
                ->where('product_type', $policy['type'])
                ->first();

            if ($subjectClause && $subjectClause['without_port_to_port_clause']) {
                return 0;
            }

            return 1;
        }

        return 0;
    }

    /**
     * 退回自动录单系统中的对应数据
     *
     * @param $orderNo
     * @param $reason
     * @return mixed
     * @throws Exception
     */
    public function sendBackInsureSys($orderNo, $reason)
    {
        if (app()->environment('local', 'development', 'staging', 'testing')) {
            return json_encode(['isSuccess' => 'Y']);
        }

        $data = [
            'token' => $this->getAcessToken(),
            'order_no' => $orderNo,
            'reason' => $reason,
        ];

        $callback = $this->httpClient($this->api_url . '/send-back', $data, 'POST');

        return $callback;
    }

    /**
     * 重提检查
     *
     * @param $orderNo
     * @return mixed
     * @throws Exception
     */
    public function resubmitCheck($orderNo)
    {
        $data = [
            'token' => $this->getAcessToken(),
            'order_no' => $orderNo
        ];

        $result = $this->httpClient($this->api_url . '/resubmit-check', $data, 'POST');

        return $result;
    }

    /**
     * 发送企业微信通知
     *
     * @param $message
     * @return void
     */
    protected function sendWeChatMessage($orderNo)
    {
        $message = $this->buildWeComMessage($orderNo);
        $config = config('baoya.capture_exception.wecom');
        $weCom = new WeCom($config);
        $weCom->send($message);
    }

    /**
     * 构建通知
     *
     * @param $orderNo
     * @return string
     */
    protected function buildWeComMessage($orderNo)
    {
        return <<<Markdown
        自动录单提交失败通知：
        >**详情**
        >流水号：{$orderNo}
        >错误信息: 请检查单票信息是否存在特殊字符
        Markdown;
    }

    /**
     * 发送邮件通知
     *
     * @param mixed $policy
     * @return void
     */
    protected function sendMailMessage($policy)
    {
        $message = '自动录单提交失败提醒 流水号:' . $policy['order_no'];
        $emails = [
            '<EMAIL>',
            '<EMAIL>',
        ];

        // 我方人员邮箱通知报备
        Mail::raw($message, function ($mail) use ($message, $emails) {
            $mail->to($emails)->subject($message);
        });

        // 保司邮箱同样报备
        if (isset($policy['product']['companyBranchAccount']['config']['report_email_recipients'])) {
            $reportEmails = explode(';', $policy['product']['companyBranchAccount']['config']['report_email_recipients']);
            // $reportEmails = ['<EMAIL>'];

            $reportEmails = array_merge($emails, $reportEmails);

            Notification::route('mail', $reportEmails)
                ->notify(new PolicyInsureSubmitFailNotifiication($policy));
        }

    }
}
