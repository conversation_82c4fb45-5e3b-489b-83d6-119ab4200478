<?php

namespace App\Support;


use App\Models\Currency;
use App\Models\Policy;
use App\Models\Product;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Storage;

trait PrepareSettings
{
    public function getSetting($policy, $policyCargo, $product)
    {
        switch ($product['company']['identifier']) {
            case 'PICC':
                $settings = $this->getPICCSettings($policy, $policyCargo, $product);
                break;
            case 'CPIC':
                $settings = $this->getCPICSettings($product);
                break;
            case 'PINGAN':
                $settings = $this->getPINGANSettings($policy, $product);
                break;
            case 'SINOSIG':
                $settings = $this->getSIONSIGSettings($policy, $policyCargo, $product);
                break;
            case 'HUATAI':
                $settings = $this->getHUATAISettings($policy, $product);
                break;
            case 'TPIC':
                $settings = $this->getTPICSettings($policy, $product);
                break;
            case 'DIC':
                $settings = $this->getDICSettings($policy, $policyCargo, $product);
                break;
            default:
                $settings = [];
        }
        return implode('|#|', $settings);
    }

    /**
     * 人保配置
     *
     * @param $policy
     * @param $policyCargo
     * @param $product
     * @return array
     */
    protected function getPICCSettings($policy, $policyCargo, $product)
    {
        $clauses = $this->getClauses($product);
        if (in_array($policy['type'], [Policy::TYPE_INTL, Policy::TYPE_CBEC])) {
            $clauses = $this->getClauses($policy);
        }
        $data = [
            $product['companyBranchAccount']['username'], // 账号
            $product['companyBranchAccount']['password'], // 密码
            $policyCargo['goodsType']['value'], // 大类
            $this->getDefaultGoodsTypeSmall($product['company']['identifier'], $policyCargo['goodsType']['value']), // 小类
        ];

        if ($product['type'] == Product::TYPE_DOMESTIC) {
            $data = array_merge($data, [
                $clauses['main'], // 主险
                $clauses['additional'][0] ?? 0, // 盗抢险 不附加则填0
            ]);
        }
        $data = array_merge($data, [
            $product['additional']['config']['agreement'] // 保单号头
        ]);

        if (!empty($product['additional']['config']['plan'])) {
            $data = array_merge($data, [
                explode('-', $product['additional']['config']['plan'])[0]
            ]);
        }

        return $data;
    }

    /**
     * 太保配置
     *
     * @param $product
     * @return array
     */
    protected function getCPICSettings($product)
    {
        return [
            $product['companyBranchAccount']['username'], // 账号
            $product['companyBranchAccount']['password'], // 密码
            $product['additional']['config']['agreement'] // 协议ID
        ];
    }

    /**
     * 平安配置
     *
     * @param $policy
     * @param $product
     * @return array
     */
    protected function getPINGANSettings($policy, $product)
    {
        $clauses = $this->getClauses($product);
        if (in_array($policy['type'], [Policy::TYPE_INTL, Policy::TYPE_CBEC])) {
            $clauses = $this->getClauses($policy);
        }
        $clauseFlag = [
            'PL0900024' => '基本险',
            'PL0900025' => '综合险',
            'PL0900026' => '航空运输险',
            'PL0900027' => '航空运输一切险',
            'PL0900028' => '陆运险',
            'PL0900029' => '陆运一切险',
            'PL0900030' => '平安险',
            'PL0900031' => '水渍险',
            'PL0900032' => '一切险',
            'PL0900033' => '冷藏险',
            'PL0900034' => '冷藏一切险',
            'PL0900040' => 'INSTITUTE CARGO CLAUSES (A)1982',
            'PL0900041' => 'INSTITUTE CARGO CLAUSES (B)1982',
            'PL0900042' => 'INSTITUTE CARGO CLAUSES (C)1982',
            'PL0900044' => 'INSTITUTE CARGO CLAUSES (AIR)',
            'PL0900064' => '陆上运输冷藏货物保险',
        ];

        $data = [
            $product['companyBranchAccount']['username'], // 账号
            $product['companyBranchAccount']['password'], // 密码
        ];
        if ($product['type'] == Product::TYPE_DOMESTIC) {
            if (($product['additional']['config']['is_new'] ?? '0') == '1') {
                $planCode = explode('-', $product['additional']['config']['plan'])[0];
                $data = array_merge($data, [
                    $product['additional']['config']['agreement'],  // 协议
                    explode('PL', $planCode)[0],  // 方案代码
                    'PL' . explode('PL', $planCode)[1],  // 险种代码
                ]);
            } else {
                $data = array_merge($data, [
                    $product['additional']['config']['agreement'],  // 协议
                    $product['additional']['config']['plan'],  // 方案 中文
                    $clauseFlag[$clauses['main']], // 主险
                ]);
            }
        }
        if (in_array($product['type'], [Policy::TYPE_INTL, Policy::TYPE_CBEC])) {
            if (($product['additional']['config']['is_new'] ?? '0') == '1') {
                $plans = explode('|', $product['additional']['config']['plan']);
                $plan = $policy['policyCargo']['insure_type'] === 1 ? $plans[0] : $plans[1];  // 出口|进口
                $planCode = explode('-', $plan)[0];
                $agreement = explode('|', $product['additional']['config']['agreement']);
                $data = array_merge($data, [
                    $policy['policyCargo']['insure_type'] === 1 ? $agreement[0] : $agreement[1],  // 协议
                    $planCode,  // 方案代码
                    $clauses['main'],  // 主险
                ]);
            } else {
                $agreement = explode('|', $product['additional']['config']['agreement']);
                $plan = $product['additional']['config']['plan'];
                if (Str::contains($product['additional']['config']['plan'], '|')) {
                    $plans = explode('|', $product['additional']['config']['plan']);
                    $plan = $policy['policyCargo']['insure_type'] === 1 ? $plans[0] : $plans[1];
                }
                $data = array_merge($data, [
                    $policy['policyCargo']['insure_type'] === 1 ? $agreement[0] : $agreement[1], // 协议
                    $plan,  // 方案 中文
                    $clauseFlag[$clauses['main']], // 主险
                ]);

                return $data;
            }
        }

        if (!empty($clauses['additional'][0])) {
            $additionals = $clauses['additional'];
            $additionals = implode(',', $additionals);
            $data = array_merge($data, [
                $additionals
            ]);
        }

        return $data;
    }

    /**
     * 阳光配置
     *
     * @param $policy
     * @param $policyCargo
     * @param $product
     * @return array
     */
    protected function getSIONSIGSettings($policy, $policyCargo, $product)
    {
        $clauses = $this->getClauses($product);
        if (in_array($policy['type'], [Policy::TYPE_INTL, Policy::TYPE_CBEC])) {
            $clauses = $this->getClauses($policy);
        }
        $data = [
            $product['additional']['config']['agreement'], // 协议
            $product['additional']['config']['plan'], // 方案
            $policyCargo['transportMethod']['value'], // 运输方式代码
            $policyCargo['goodsType']['value'], // 货物类别代码
            $policyCargo['goodsType']['name'], // 货物类别名称
            $clauses['main'],  // 主险代码
        ];
        if (!empty($clauses['additional'][0])) {
            array_merge($data, [
                'DQX'
            ]);
        }
        return $data;
    }

    /**
     * 华泰接口配置
     *
     * @param $policy
     * @param $product
     * @return array
     */
    protected function getHUATAISettings($policy, $product)
    {
        $clauses = $this->getClauses($product);
        if (in_array($policy['type'], [Policy::TYPE_INTL, Policy::TYPE_CBEC])) {
            $clauses = $this->getClauses($policy);
        }
        $data = [
            $product['companyBranchAccount']['username'], // 账号
            $product['additional']['config']['agreement'], // 协议
            $product['additional']['config']['plan'], // 方案
            $clauses['main'] // 险种代码
        ];
        if (!empty($clauses['additional'][0])) {
            $data = array_merge($data, [
                implode(',', $clauses['additional'])
            ]);
        }

        return $data;
    }

    /**
     * 获取太平配置
     *
     * @param $policy
     * @param $product
     * @return array
     */
    protected function getTPICSettings($policy, $product)
    {
        $account = $product['companyBranchAccount']['username'];
        $password = $product['companyBranchAccount']['password'];
        if ($product['type'] == Product::TYPE_DOMESTIC) {
            $agreementData = $product['additional']['config']['agreement'];
            $agreementData = json_decode($agreementData, true);
            if (empty($agreementData[$policy['policyCargo']['transportMethod']['value']])) {
                abort(400, '投保异常,请联系管理员处理');
            }
            $agreementData = $agreementData[$policy['policyCargo']['transportMethod']['value']];
            $agreementData = explode('-', $agreementData);
            $agreement = $agreementData[0];
        } else {
            $agreementData = $product['additional']['config']['agreement'];
            $agreementData = json_decode($agreementData, true);
            $agreementData = $policy['policyCargo']['insure_type'] === 1 ? $agreementData['EXPORT'] : $agreementData['IMPORT'];
            if (empty($agreementData[$policy['policyCargo']['transportMethod']['value']])) {
                abort(400, '投保异常,请联系管理员处理');
            }
            $agreementData = $agreementData[$policy['policyCargo']['transportMethod']['value']];
            $agreementData = explode('-', $agreementData);
            $agreement = $agreementData[0];
        }
        $data = [
            $account, // 账号
            $password, // 密码
            $agreement, // 协议
        ];

        return $data;
    }

    /**
     * 获取东海录单配置信息
     *
     * @param $policy
     * @param $policyCargo
     * @param $product
     * @return array
     */
    protected function getDICSettings($policy, $policyCargo, $product)
    {
        $data = [
            $product['additional']['config']['plan'],
            $policyCargo['goodsType']['value'] . '-' . $policyCargo['goodsType']['name']

        ];

        return $data;
    }

    /**
     * 获取小类
     *
     * @param $companyIdentifier
     * @param $goodsType
     * @return array|int|mixed
     */
    protected function getDefaultGoodsTypeSmall($companyIdentifier, $goodsType)
    {
        switch ($companyIdentifier) {
            case 'PICC':
                $data = [
                    '粮食、食品、果蔬、饮料、烟草及饲料' => '624',
                    '轻纺、文体、日用品、工艺品' => '804',
                    '医药类' => '884',
                    '金属原料、矿产及建材类、玻璃及其制品' => '738',
                    '其它' => '899',
                    '木、木浆及木制品；纸、纸浆、纸板及其制品' => '880',
                    '石油、化工原料及其制品' => '745',
                    '电器、机械、运输工具、设备类' => '849',
                ];
                break;
            case 'PINGAN':
                $data = [
                    '01' => '0105',
                    '02' => '1001',
                    '03' => '1501',
                    '04' => '1601',
                    '05' => '2501',
                    '06' => '2801',
                    '07' => '3901',
                    '08' => '4101',
                    '09' => '4401',
                    '10' => '4701',
                    '11' => '5001',
                    '12' => '6401',
                    '15' => '7201',
                    '16' => '8412',
                    '17' => '8601',
                    '18' => '9001',
                    '20' => '9401',
                ];
                break;
            case 'CPIC':
                $data = [
                    '五金类' => '0513',
                    '农产品、土畜产类' => '0217',
                    '动植物油脂' => '1602',
                    '化工品类' => '0917',
                    '工艺品类' => '0602',
                    '木材、造纸' => '1106',
                    '机械设备类' => '0711',
                    '电子产品' => '0810',
                    '矿产类' => '0505',
                    '粮谷类' => '0111',
                    '纺织品' => '0405',
                    '轻工品类' => '0310',
                    '非食用油' => '1501',
                    '饲料类' => '0108',
                ];
                break;
            default:
                $data = [];
        }

        return !empty($data[$goodsType]) ? $data[$goodsType] : 0;
    }

    /**
     * 获取保单/产品条款
     *
     * @param $model
     * @return array
     */
    protected function getClauses($model)
    {
        $clauses = [];
        foreach ($model['clauses'] as $clause) {
            if ($clause['parent_id'] == '-1') {
                $clauses['main'] = $clause['identifier'];
            } else {
                $clauses['additional'][] = $clause['identifier'];
            }
        }

        return $clauses;
    }

    /**
     * 是否是制裁国家
     *
     * @param $product
     * @param $policy
     * @return bool
     */
    protected function isSanctionists($product, $policy)
    {
        $isSanctionists = explode('|', $product['additional']['sanctionist']);
        $departure = in_array($product['type'], [Product::TYPE_INTL, Product::TYPE_CBEC])
            ? $policy['policyCargo']['departure']
            : explode('-', $policy['policyCargo']['departure'])[0];
        $destination = in_array($product['type'], [Product::TYPE_INTL, Product::TYPE_CBEC])
            ? $policy['policyCargo']['destination']
            : explode('-', $policy['policyCargo']['destination'])[0];

        if (in_array($departure, $isSanctionists) || in_array($destination, $isSanctionists)) {
            return true;
        }

        return false;
    }

    /**
     * 国内字段整理
     *
     * @param mixed $policy
     * @param mixed $policyCargo
     * @return array
     */
    protected function arrangeField($policy, $policyCargo)
    {
        // if ($policy['user']['is_use_insure_preset_data'] == 1 && !(in_array($policy['user']['platform_id'], [1, 2]) && $policy['company']['identifier'] == 'PINGAN')) {
        if ($policy['user']['is_use_insure_preset_data']) {
            $presetData = json_decode($policy['user']['insure_preset_data'], true);

            $policy['policyholder'] = !empty($presetData['policyholder']) ? $presetData['policyholder'] : $policy['policyholder'];
            $policy['policyholder_type'] = !empty($presetData['policyholder_type']) ? $presetData['policyholder_type'] : $policy['policyholder_type'];
            $policy['policyholder_idcard_no'] = !empty($presetData['policyholder_idcard_no']) ? $presetData['policyholder_idcard_no'] : $policy['policyholder_idcard_no'];
            $policy['policyholder_phone_number'] = !empty($presetData['policyholder_phone_number']) ? $presetData['policyholder_phone_number'] : $policy['policyholder_phone_number'];
            $policy['policyholder_address'] = !empty($presetData['policyholder_address']) ? $presetData['policyholder_address'] : $policy['policyholder_address'];
            $policy['policyholder_idcard_issue_date'] = !empty($presetData['policyholder_idcard_issue_date']) ? $presetData['policyholder_idcard_issue_date'] : $policy['policyholder_idcard_issue_date'];
            $policy['policyholder_idcard_valid_till'] = !empty($presetData['policyholder_idcard_valid_till']) ? $presetData['policyholder_idcard_valid_till'] : $policy['policyholder_idcard_valid_till'];

            $policy['insured'] = !empty($presetData['insured']) ? $presetData['insured'] : $policy['insured'];
            $policy['insured_type'] = !empty($presetData['insured_type']) ? $presetData['insured_type'] : $policy['insured_type'];
            $policy['insured_idcard_no'] = !empty($presetData['insured_idcard_no']) ? $presetData['insured_idcard_no'] : $policy['insured_idcard_no'];
            $policy['insured_phone_number'] = !empty($presetData['insured_phone_number']) ? $presetData['insured_phone_number'] : $policy['insured_phone_number'];
            $policy['insured_address'] = !empty($presetData['insured_address']) ? $presetData['insured_address'] : $policy['insured_address'];
            $policy['insured_idcard_issue_date'] = !empty($presetData['insured_idcard_issue_date']) ? $presetData['insured_idcard_issue_date'] : $policy['insured_idcard_issue_date'];
            $policy['insured_idcard_valid_till'] = !empty($presetData['insured_idcard_valid_till']) ? $presetData['insured_idcard_valid_till'] : $policy['insured_idcard_valid_till'];
        }

        $fromLoc = $policyCargo['departure'];
        $viaLoc = $policyCargo['transmit'];
        $toLoc = $policyCargo['destination'];

        if ($policy['company']['identifier'] != 'CPIC') {
            $fromLoc = str_replace(':', '-', $policyCargo['departure']);
            $viaLoc = str_replace(':', '-', $policyCargo['transmit']);
            $toLoc = str_replace(':', '-', $policyCargo['destination']);
        }

        $data = [
            'flag' => 'insure',
            'subject' => $policy['policyCargo']['subject']['identifier'] ?? '',
            //投保人
            'holderName' => $policy['policyholder'],
            'holderCustomerType' => $policy['policyholder_type'],
            'holderOverseas' => $policy['policyholder_overseas'],
            'holderPhone' => $policy['policyholder_phone_number'],
            'holderAddr' => $policy['policyholder_address'],
            'holderIdenty' => empty($policy['policyholder_idcard_no']) ? $policy['user']['idcard_no'] : $policy['policyholder_idcard_no'],
            'holderStartDateValid' => $policy['policyholder_idcard_issue_date'],
            'holderEndDateValid' => $policy['policyholder_idcard_valid_till'],

            //被保人
            'recognizeeName' => $policy['insured'],
            'recognizeeCustomerType' => $policy['insured_type'],
            'recognizeeOverseas' => $policy['insured_overseas'],
            'recognizeePhone' => $policy['insured_phone_number'],
            'recognizeeAddr' => $policy['insured_address'],
            'recognizeeIdenty' => $policy['insured_idcard_no'],
            'recognizeeStartDateValid' => $policy['insured_idcard_issue_date'],
            'recognizeeEndDateValid' => $policy['insured_idcard_valid_till'],
            //货物信息
            'orderNo' => $policy['order_no'],
            'invNo' => $policyCargo['invoice_no'],
            'freightNo' => $policyCargo['waybill_no'],
            'goodsName' => $policyCargo['goods_name'],
            'quantity' => $policyCargo['goods_amount'],
            'goodsType' => $policyCargo['goodsType']['value'],
            'pack' => $policyCargo['packingMethod']['value'],
            'nudePackClause' => '裸装：本保单不承保由于刮擦，锈损，凹瘪引起的损失。',
            //运输信息
            'transport' => $policyCargo['transport_no'],
            'fromLoc' => $fromLoc,
            'fromLocPort' => $policyCargo['departure_port'],
            'viaLoc' => $viaLoc,
            'viaLocPort' => $policyCargo['transmit_port'],
            'toLoc' => $toLoc,
            'toLocPort' => $policyCargo['destination_port'],
            'departureDate' => $policyCargo['shipping_date'],
            'transportTypeID' => $policyCargo['transportMethod']['value'],  //人保系统使用 运输方式ID
            'loadType' => $policyCargo['loadingMethod']['value'],
            'makeYearMonth' => $policyCargo['ship_construction_year'],
            //金额信息
            "insuredAmount" => $policy['coverage'],
            "premium" => $policy['user_premium'],
            "ratio" => $policy['user_rate'],
            "premium_sync" => $policy['is_premium_sync'],
            'withSpecialAndDeductible' => $policyCargo['with_user_special_and_deductible'] ?? '',
            'customDeductible' => $policy['custom_deductible'] ?? '',
            'consignor' => $policyCargo['consignor'],
            'consignee' => $policyCargo['consignee'],
            'carrier' => $policyCargo['carrier'],
        ];

        $data = $this->handleAntiDatedFile($policy, $data);

        if ($policy['is_premium_sync'] == 0) {
            $data['premium'] = $policy['premium'];
            $data['ratio'] = $policy['rate'];
        }

        if ($policy['company']['identifier'] === 'PICC') {  // 人保系统现在无法手动录入保费,保费同步功能无法生效,直接以保司最低保费为准
            $data['premium'] = $this->calcPremium($policy['coverage'], $policy['rate'], $policy['minimum_premium']);
            $data['ratio'] = $policy['rate'];
        }

        $data['insuredAmount'] = round(bcdiv($data['insuredAmount'], 100, 5), 2);
        $data['premium'] = round(bcdiv($data['premium'], 100, 5), 2);

        return $data;
    }

    /**
     * 国际字段整理
     *
     * @param mixed $policy
     * @param mixed $policyCargo
     * @return array
     */
    public function arrangeFieldIntl($policy, $policyCargo)
    {
        // if ($policy['user']['is_use_insure_preset_data'] == 1 && !(in_array($policy['user']['platform_id'], [1, 2]) && $policy['company']['identifier'] == 'PINGAN')) {
        if ($policy['user']['is_use_insure_preset_data']) {
            $presetData = json_decode($policy['user']['insure_preset_data'], true);

            $policy['policyholder'] = !empty($presetData['policyholder']) ? $presetData['policyholder'] : $policy['policyholder'];
            $policy['policyholder_type'] = !empty($presetData['policyholder_type']) ? $presetData['policyholder_type'] : $policy['policyholder_type'];
            $policy['policyholder_idcard_no'] = !empty($presetData['policyholder_idcard_no']) ? $presetData['policyholder_idcard_no'] : $policy['policyholder_idcard_no'];
            $policy['policyholder_phone_number'] = !empty($presetData['policyholder_phone_number']) ? $presetData['policyholder_phone_number'] : $policy['policyholder_phone_number'];
            $policy['policyholder_address'] = !empty($presetData['policyholder_address']) ? $presetData['policyholder_address'] : $policy['policyholder_address'];
            $policy['policyholder_idcard_issue_date'] = !empty($presetData['policyholder_idcard_issue_date']) ? $presetData['policyholder_idcard_issue_date'] : $policy['policyholder_idcard_issue_date'];
            $policy['policyholder_idcard_valid_till'] = !empty($presetData['policyholder_idcard_valid_till']) ? $presetData['policyholder_idcard_valid_till'] : $policy['policyholder_idcard_valid_till'];

            $policy['insured'] = !empty($presetData['insured']) ? $presetData['insured'] : $policy['insured'];
            $policy['insured_type'] = !empty($presetData['insured_type']) ? $presetData['insured_type'] : $policy['insured_type'];
            $policy['insured_idcard_no'] = !empty($presetData['insured_idcard_no']) ? $presetData['insured_idcard_no'] : $policy['insured_idcard_no'];
            $policy['insured_phone_number'] = !empty($presetData['insured_phone_number']) ? $presetData['insured_phone_number'] : $policy['insured_phone_number'];
            $policy['insured_address'] = !empty($presetData['insured_address']) ? $presetData['insured_address'] : $policy['insured_address'];
            $policy['insured_idcard_issue_date'] = !empty($presetData['insured_idcard_issue_date']) ? $presetData['insured_idcard_issue_date'] : $policy['insured_idcard_issue_date'];
            $policy['insured_idcard_valid_till'] = !empty($presetData['insured_idcard_valid_till']) ? $presetData['insured_idcard_valid_till'] : $policy['insured_idcard_valid_till'];
        }

        $clauses = $this->getClauses($policy);

        // 添加制裁条款
        if ($policy['company']['identifier'] == 'PINGAN' && $this->isSanctionists($policy['product'], $policy)) {
            $clauses['additional'][] = 'PL0900090';
        }

        foreach ($policy as &$val) {
            $val = str_replace(["\r\n", "\r", "\n", "\t"], '', $val);
        }
        foreach ($policyCargo as &$value) {
            $value = str_replace(["\r\n", "\r", "\n", "\t"], '', $value);
        }

        $currency = Currency::find($policyCargo['coverage_currency_id']);

        $currencyRate = $currency['rate'];

        // 临时处理 人保节假日汇率未更新,暂时先以上月汇率处理
        // if ($policy['company']['identifier'] === 'PICC') {
        //     // 获取上个月的第一天
        //     $lastMonthFirstDay = Carbon::now()->subMonth()->startOfMonth()->format('Y-m-d');

        //     $_currency = Currency::withTrashed()
        //         ->where('code', $currency['code'])
        //         ->where('date', $lastMonthFirstDay)
        //         ->first();
        //     $currencyRate = $_currency['rate'];
        // }

        $data = [
            'flag' => 'insure',
            'subject' => $policy['policyCargo']['subject']['identifier'] ?? '',
            'insureType' => $this->getInsureType($policyCargo['insure_type']), // 投保类型
            'planName' => ($policy['product']['additional']['config']['is_new'] ?? '0') == '1' ? $policy['product']['additional']['config']['plan'] : '',
            'orderNo' => $policy['order_no'],
            'isInvoice' => '0', // 是否开发票

            //投保人
            'holderName' => $policy['policyholder'],
            'holderCustomerType' => $policy['policyholder_type'],
            'holderOverseas' => $policy['policyholder_overseas'],
            'holderPhone' => $policy['policyholder_phone_number'],
            'holderAddr' => $policy['policyholder_address'],
            'holderIdenty' => empty($policy['policyholder_idcard_no']) ? $policy['user']['idcard_no'] : $policy['policyholder_idcard_no'],
            'holderStartDateValid' => $policy['policyholder_idcard_issue_date'],
            'holderEndDateValid' => $policy['policyholder_idcard_valid_till'],

            //被保人
            'recognizeeName' => $policy['insured'],
            'recognizeeCustomerType' => $policy['insured_type'],
            'recognizeeOverseas' => $policy['insured_overseas'],
            'recognizeePhone' => $policy['insured_phone_number'],
            'recognizeeAddr' => $policy['insured_address'],
            'recognizeeIdenty' => $policy['insured_idcard_no'],
            'recognizeeStartDateValid' => $policy['insured_idcard_issue_date'],
            'recognizeeEndDateValid' => $policy['insured_idcard_valid_till'],

            // 货物信息
            'quantity' => $policyCargo['goods_amount'], //包装及数量
            'shipment_id' => $policyCargo['shipment_id'], //shipment_id
            'goodsName' => $policyCargo['goods_name'], //货物明细
            'goodsType' => $policyCargo['goodsType']['value'],  //货物类型
            'shippingMark' => $policyCargo['shipping_mark'],  //唛头
            'packType' => $policyCargo['packingMethod']['value'], //包装方式
            'nudePackClause' => 'UNPACKED CARGO EXCL.RUSTING,SCRAPING AND DENT.',
            'invoiceNo' => $policyCargo['invoice_no'], //发票号
            'landBillNo' => $policyCargo['waybill_no'], //提运单号
            'contractNo' => $policyCargo['contract_no'], //合同号
            'transportNo' => $policyCargo['transport_no'], //运输工具名称
            'loadType' => $policyCargo['loadingMethod']['value'], //装载方式
            'transportType' => $policyCargo['transportMethod']['value'], //运输方式
            'fromCountry' => $policyCargo['departure'], //起运国家
            'viaCountry' => $policyCargo['transmit'], //中转地国家
            'toCountry' => $policyCargo['destination'], //目的地国家
            'fromLoc' => $policyCargo['departure_port'], //起运地详细地址
            'viaLoc' => $policyCargo['transmit_port'], //中转地详细地址
            'toLoc' => $policyCargo['destination_port'], //目的地详细地址
            'payLoc' => $policyCargo['payable_at'], //赔付地
            'isCredit' => $policyCargo['is_credit'], //是否做信用证
            'creditNo' => $policyCargo['credit_no'], //信用证号
            'departureDate' => $policyCargo['shipping_date'], //起运日期
            'makeYearMonth' => $policyCargo['ship_construction_year'], // 船舶建造年
            'antiDateDay' => $policy['product']['additional']['anti_dated_days'] ?? '-1', //允许倒签时间
            'timeFormat' => $policyCargo['shipping_date_print_format'], //起运日期打印格式
            'mainClause' => $clauses['main'], //主险
            'additionClause' => $clauses['additional'] ?? [], //附加险
            'clause' => $policyCargo['main_clause'] . "\n" . $policyCargo['additional_clause'], //条款内容
            'mainClauseContent' => $policyCargo['main_clause'], //主险条款内容
            'additionClauseContent' => $policyCargo['additional_clause'], //副险条款内容
            'otherClause' => $policyCargo['clause_content'], //其他凭证
            'comments' => '', //特殊需求说明
            'invAmount' => $policyCargo['invoice_amount'], //发票金额
            'amount' => $policy['coverage'], //保额
            'rate' => $policy['user_rate'], //费率
            'premium' => $this->calcPremium($policy['coverage'], $policy['user_rate'], round(bcdiv($policy['user_minimum_premium'], $currencyRate, 5), 2)), //保费
            'cny_premium' => readable_amount($policy['user_premium']), //人民币保费
            "premium_sync" => $policy['is_premium_sync'],
            "is_only_port_regions" => $this->isOnlyPortRegions($policy),
            'invCurrency' => $policyCargo['invoiceCurrency']['code'], //发票币种
            'incRate' => $policyCargo['bonus'], //加成比例
            'invHead' => '', //发票抬头
            'invHeadLicence' => '', //开票人纳税人识别号
            'usdExchange' => '',
            'usdPremium' => '',
            'premiumCurrency' => 'CNY',
            'withSpecialAndDeductible' => $policyCargo['with_user_special_and_deductible'] ?? '',
            'customDeductible' => $policy['custom_deductible'] ?? '',
            'consignor' => $policyCargo['consignor'],
            'consignee' => $policyCargo['consignee'],
            'carrier' => $policyCargo['carrier'],
        ];

        $data = $this->handleAntiDatedFile($policy, $data);


        if (!in_array($policyCargo['invoiceCurrency']['code'], ['HKD', 'CNY', 'USD'])) {
            $data['usdExchange'] = Currency::where('code', 'USD')->orderBy('id', 'desc')->first()['rate'];
            $data['usdPremium'] = bcdiv(round(bcdiv($policy['is_premium_sync'] == 1 ? $policy['user_premium'] : $policy['premium'], $data['usdExchange'], 5), 3), 100, 2);
        }

        if ($policy['is_premium_sync'] == 0 || $policy['company']['identifier'] === 'PICC') {  // 人保系统现在无法手动录入保费,保费同步功能无法生效,直接以保司最低保费为准
            $data['premium'] = $this->calcPremium($policy['coverage'], $policy['rate'], round(bcdiv($policy['minimum_premium'], $currencyRate, 5), 2));
            $data['cny_premium'] = readable_amount($policy['premium']);
            $data['rate'] = $policy['rate'];
        }

        if (($policy['product']['additional']['config']['premium_currency'] ?? '') == 'BY_AMOUNT') {
            $data['premiumCurrency'] = $data['invCurrency'];
        }

        $data['invAmount'] = round(bcdiv($data['invAmount'], 100, 5), 2);
        $data['amount'] = round(bcdiv($data['amount'], 100, 5), 2);
        $data['premium'] = round(bcdiv($data['premium'], 100, 5), 2);



        return $data;
    }

    /**
     * 单车责任险字段整理
     *
     * @param mixed $policy
     * @param mixed $policyCargo
     * @return array
     */
    protected function arrangeFieldLbt($policy, $policyCargo)
    {
        // if ($policy['user']['is_use_insure_preset_data'] == 1 && !(in_array($policy['user']['platform_id'], [1, 2]) && $policy['company']['identifier'] == 'PINGAN')) {
        if ($policy['user']['is_use_insure_preset_data']) {
            $presetData = json_decode($policy['user']['insure_preset_data'], true);

            $policy['policyholder'] = !empty($presetData['policyholder']) ? $presetData['policyholder'] : $policy['policyholder'];
            $policy['policyholder_type'] = !empty($presetData['policyholder_type']) ? $presetData['policyholder_type'] : $policy['policyholder_type'];
            $policy['policyholder_idcard_no'] = !empty($presetData['policyholder_idcard_no']) ? $presetData['policyholder_idcard_no'] : $policy['policyholder_idcard_no'];
            $policy['policyholder_phone_number'] = !empty($presetData['policyholder_phone_number']) ? $presetData['policyholder_phone_number'] : $policy['policyholder_phone_number'];
            $policy['policyholder_address'] = !empty($presetData['policyholder_address']) ? $presetData['policyholder_address'] : $policy['policyholder_address'];
            $policy['policyholder_idcard_issue_date'] = !empty($presetData['policyholder_idcard_issue_date']) ? $presetData['policyholder_idcard_issue_date'] : $policy['policyholder_idcard_issue_date'];
            $policy['policyholder_idcard_valid_till'] = !empty($presetData['policyholder_idcard_valid_till']) ? $presetData['policyholder_idcard_valid_till'] : $policy['policyholder_idcard_valid_till'];

            $policy['insured'] = !empty($presetData['insured']) ? $presetData['insured'] : $policy['insured'];
            $policy['insured_type'] = !empty($presetData['insured_type']) ? $presetData['insured_type'] : $policy['insured_type'];
            $policy['insured_idcard_no'] = !empty($presetData['insured_idcard_no']) ? $presetData['insured_idcard_no'] : $policy['insured_idcard_no'];
            $policy['insured_phone_number'] = !empty($presetData['insured_phone_number']) ? $presetData['insured_phone_number'] : $policy['insured_phone_number'];
            $policy['insured_address'] = !empty($presetData['insured_address']) ? $presetData['insured_address'] : $policy['insured_address'];
            $policy['insured_idcard_issue_date'] = !empty($presetData['insured_idcard_issue_date']) ? $presetData['insured_idcard_issue_date'] : $policy['insured_idcard_issue_date'];
            $policy['insured_idcard_valid_till'] = !empty($presetData['insured_idcard_valid_till']) ? $presetData['insured_idcard_valid_till'] : $policy['insured_idcard_valid_till'];
        }

        $data = [
            'flag' => 'insure',
            //投保人
            'holderName' => $policy['policyholder'],
            'holderCustomerType' => $policy['policyholder_type'],
            'holderOverseas' => $policy['policyholder_overseas'],
            'holderPhone' => $policy['policyholder_phone_number'],
            'holderAddr' => $policy['policyholder_address'],
            'holderIdenty' => empty($policy['policyholder_idcard_no']) ? $policy['user']['idcard_no'] : $policy['policyholder_idcard_no'],
            'holderStartDateValid' => $policy['policyholder_idcard_issue_date'],
            'holderEndDateValid' => $policy['policyholder_idcard_valid_till'],

            //被保人
            'recognizeeName' => $policy['insured'],
            'recognizeeCustomerType' => $policy['insured_type'],
            'recognizeeOverseas' => $policy['insured_overseas'],
            'recognizeePhone' => $policy['insured_phone_number'],
            'recognizeeAddr' => $policy['insured_address'],
            'recognizeeIdenty' => $policy['insured_idcard_no'],
            'recognizeeStartDateValid' => $policy['insured_idcard_issue_date'],
            'recognizeeEndDateValid' => $policy['insured_idcard_valid_till'],
            //货物信息
            'orderNo' => $policy['order_no'],
            'invNo' => $policyCargo['invoice_no'],
            'freightNo' => $policyCargo['waybill_no'],
            'goodsName' => $policyCargo['goods_name'],
            'quantity' => $policyCargo['goods_amount'],
            'goodsType' => $policyCargo['goodsType']['value'],
            'pack' => $policyCargo['packingMethod']['value'],
            //运输信息
            'transport' => $policyCargo['transport_no'],
            'fromLoc' => str_replace(':', '-', $policyCargo['departure']),
            'fromLocPort' => $policyCargo['departure_port'],
            'viaLoc' => str_replace(':', '-', $policyCargo['transmit']),
            'viaLocPort' => $policyCargo['transmit_port'],
            'toLoc' => str_replace(':', '-', $policyCargo['destination']),
            'toLocPort' => $policyCargo['destination_port'],
            'departureDate' => $policyCargo['shipping_date'],
            'transportTypeID' => $policyCargo['transportMethod']['value'],  //人保系统使用 运输方式ID
            'loadType' => $policyCargo['loadingMethod']['value'],
            'makeYearMonth' => $policyCargo['ship_construction_year'],
            //金额信息
            "insuredAmount" => $policy['coverage'],
            "premium" => $policy['user_premium'],
            "ratio" => $policy['user_rate'],
            "premium_sync" => $policy['is_premium_sync'],
        ];

        $data = $this->handleAntiDatedFile($policy, $data);

        if ($policy['is_premium_sync'] == 0) {
            $data['premium'] = $policy['premium'];
            $data['ratio'] = $policy['rate'];
        }

        if ($policy['company']['identifier'] === 'PICC') {  // 人保系统现在无法手动录入保费,保费同步功能无法生效,直接以保司最低保费为准
            $data['premium'] = $this->calcPremium($policy['coverage'], $policy['rate'], $policy['minimum_premium']);
            $data['ratio'] = $policy['rate'];
        }

        $data['insuredAmount'] = round(bcdiv($data['insuredAmount'], 100, 5), 2);
        $data['premium'] = round(bcdiv($data['premium'], 100, 5), 2);

        return $data;
    }

    /**
     * 是否倒签
     *
     * @param $policy
     * @param $product
     * @return bool
     */
    protected function isBackdating($policy, $product)
    {
        $antiDatedDays = ($product['additional']['anti_dated_days'] ?? 0);
        $submittedAt = Carbon::parse($policy['submitted_at']);
        $shippingDate = Carbon::parse($policy['policyCargo']['shipping_date']);
        if ($submittedAt->startOfDay()->gt($shippingDate->addDays($antiDatedDays)->startOfDay())) {
            return true;
        }
        return false;
    }

    /**
     * 处理倒签保函文件
     *
     * @param mixed $policy
     * @param mixed $data
     * @return mixed
     */
    protected function handleAntiDatedFile($policy, $data)
    {
        $data['antiDatedFileName'] = $policy['policyCargo']['anti_dated_file']
            ? '倒签保函.' . pathinfo($policy['policyCargo']['anti_dated_file'], PATHINFO_EXTENSION)
            : '';
        $data['antiDatedFile'] = $policy['policyCargo']['anti_dated_file'] && Storage::disk('public')->exists($policy['policyCargo']['anti_dated_file'])
            ? base64_encode(Storage::disk('public')->get($policy['policyCargo']['anti_dated_file']))
            : '';

        // 平安深圳 平安广东 如果倒签了但是没有超出允许倒签时间 给一个默认文件
        if (in_array($policy['company_branch_id'], [20, 29])) {
            if (date('Y-m-d', strtotime($policy['policyCargo']['shipping_date'])) < date('Y-m-d') && !$this->isBackdating($policy, $policy['product'])) {
                $data['antiDatedFileName'] = '_倒签保函.png';
                $data['antiDatedFile'] = base64_encode(file_get_contents(resource_path('static/img/anti_dated_file.png')));
            }
        }
        return $data;
    }
}
