name: Build and Deploy

on:
  push:
    branches:
      - main
      - develop
  pull_request:
    branches:
      - main
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v5

      - name: Setup PHP
        run: |
          sed -i 's/archive.ubuntu.com/mirrors.cloud.aliyuncs.com/g' /etc/apt/sources.list.d/ubuntu.sources
          apt-get update -y
          apt-get --no-install-recommends install -y php8.3-cli php8.3-common php8.3-curl php8.3-xml
          php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');"
          # php -r "if (hash_file('sha384', 'composer-setup.php') === 'dac665fdc30fdd8ec78b38b9800061b4150413ff2e3b6f88543c636f7cd84f6db9189d43a81e5503cda447da73c7e5b6') { echo 'Installer verified'.PHP_EOL; } else { echo 'Installer corrupt'.PHP_EOL; unlink('composer-setup.php'); exit(1); }"
          php composer-setup.php --install-dir=/usr/local/bin --filename=composer --2
          php -r "unlink('composer-setup.php');"
          composer --version

      - name: Get Composer cache directory
        id: composer-cache
        run: |
          echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache Composer dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: |
            ${{ runner.os }}-composer-

      - name: Install dependencies
        run: |
          composer config http-basic.git.winray.tech ${{ secrets.PACKAGE_USERNAME }} ${{ secrets.PACKAGE_PASSWORD }}
          composer install --no-interaction --no-progress --no-dev --prefer-dist --ignore-platform-reqs
          composer dump-autoload --optimize

      - name: Tarball application
        run: tar -czf "$GITHUB_SHA.tar.gz" app bootstrap config database public lang resources routes vendor artisan composer.json composer.lock

      - name: Export environment variables for staging
        if: github.ref == 'refs/heads/develop'
        run: |
          echo "ROOT_PATH=/var/www/new_51baoya" >> $GITHUB_ENV
          echo "APP_PATH=/var/www/new_51baoya/dashboard" >> $GITHUB_ENV
          echo "SSH_HOST=${{ vars.SSH_STAGING_HOST }}" >> $GITHUB_ENV
          echo "SSH_USER=${{ vars.SSH_STAGING_USER }}" >> $GITHUB_ENV
          echo "CHOWN_USER=www-data:www-data" >> $GITHUB_ENV

      - name: Export environment variables for production
        if: github.ref == 'refs/heads/main'
        run: |
          echo "ROOT_PATH=/home/<USER>/app/baoya" >> $GITHUB_ENV
          echo "APP_PATH=/home/<USER>/app/baoya/dashboard" >> $GITHUB_ENV
          echo "SSH_HOST=${{ vars.SSH_PROD_HOST }}" >> $GITHUB_ENV
          echo "SSH_USER=${{ vars.SSH_PROD_USER }}" >> $GITHUB_ENV
          echo "CHOWN_USER=user:user" >> $GITHUB_ENV

      - name: Setup project
        env:
          SSH_HOST: ${{ env.SSH_HOST }}
          SSH_USER: ${{ env.SSH_USER }}
          ROOT_PATH: ${{ env.ROOT_PATH }}
          APP_PATH: ${{ env.APP_PATH }}
          APP_SHARED_PATH: ${{ env.APP_PATH }}/shared
          APP_CURRENT_PATH: ${{ env.APP_PATH }}/current
          APP_VERSION_PATH: ${{ env.APP_PATH }}/releases
          DOCKER_IMAGE: ${{ vars.DOCKER_PHP_IMAGE }}
          CHOWN_USER: ${{ env.CHOWN_USER }}
        run: |
          if [ "$GITHUB_REF" == "refs/heads/main" ]; then
            SSH_PRIVATE_KEY="${{ secrets.SSH_PROD_PRIVATE_KEY }}"
          else
            SSH_PRIVATE_KEY="${{ secrets.SSH_STAGING_PRIVATE_KEY }}"
          fi

          mkdir -p "$HOME/.ssh"
          echo "$SSH_PRIVATE_KEY" > "$HOME/.ssh/id_rsa"
          chmod 600 "$HOME/.ssh/id_rsa"

          scp -i "$HOME/.ssh/id_rsa" -o StrictHostKeyChecking=no "$GITHUB_SHA.tar.gz" "$SSH_USER@$SSH_HOST:$APP_VERSION_PATH/$GITHUB_SHA.tar.gz"

          ssh -i "$HOME/.ssh/id_rsa" -o StrictHostKeyChecking=no "$SSH_USER@$SSH_HOST" << EOF
            set -xe

            mkdir -p "$APP_VERSION_PATH/$GITHUB_SHA"
            tar -xzf "$APP_VERSION_PATH/$GITHUB_SHA.tar.gz" -C "$APP_VERSION_PATH/$GITHUB_SHA"
            rm "$APP_VERSION_PATH/$GITHUB_SHA.tar.gz"

            ln -sfn "$APP_VERSION_PATH/$GITHUB_SHA" "$APP_CURRENT_PATH"
            ln -sfn "$APP_SHARED_PATH/storage" "$APP_CURRENT_PATH/storage"
            ln -sfn "$APP_SHARED_PATH/.env" "$APP_CURRENT_PATH/.env"

            cd "$APP_VERSION_PATH"
            ls -t | tail -n +5 | xargs rm -rf

            docker run --rm -v "$ROOT_PATH:$ROOT_PATH" -v "$APP_CURRENT_PATH:$APP_CURRENT_PATH" -v "$APP_SHARED_PATH:$APP_SHARED_PATH" -w "$APP_CURRENT_PATH" "$DOCKER_IMAGE" php artisan storage:link
            docker run --rm -v "$ROOT_PATH:$ROOT_PATH" -v "$APP_CURRENT_PATH:$APP_CURRENT_PATH" -v "$APP_SHARED_PATH:$APP_SHARED_PATH" -w "$APP_CURRENT_PATH" "$DOCKER_IMAGE" php artisan optimize
            docker run --rm -v "$ROOT_PATH:$ROOT_PATH" -v "$APP_CURRENT_PATH:$APP_CURRENT_PATH" -v "$APP_SHARED_PATH:$APP_SHARED_PATH" -w "$APP_CURRENT_PATH" "$DOCKER_IMAGE" php artisan config:cache
            docker run --rm -v "$ROOT_PATH:$ROOT_PATH" -v "$APP_CURRENT_PATH:$APP_CURRENT_PATH" -v "$APP_SHARED_PATH:$APP_SHARED_PATH" -w "$APP_CURRENT_PATH" "$DOCKER_IMAGE" php artisan migrate --force

            sudo chown -R "$CHOWN_USER" "$APP_PATH"

            if [ "$GITHUB_REF" == "refs/heads/main" ]; then
              docker restart php-cli
            fi

            docker restart nginx
            docker restart php-fpm
          EOF
