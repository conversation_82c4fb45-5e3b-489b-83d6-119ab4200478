# 保险公司自动录单系统详细分析文档

## 目录

-   [1. 系统概述](#1-系统概述)
-   [2. 核心类和方法](#2-核心类和方法)
-   [3. 保险公司配置详解](#3-保险公司配置详解)
-   [4. 数据处理流程](#4-数据处理流程)
-   [5. 特殊业务逻辑](#5-特殊业务逻辑)
-   [6. 错误处理机制](#6-错误处理机制)
-   [7. 重构建议](#7-重构建议)

## 1. 系统概述

### 1.1 文件结构

-   **PrepareSettings.php**: 负责不同保险公司的配置参数准备
-   **AutoInsure.php**: 负责自动录单的主要业务逻辑和数据处理

### 1.2 主要功能

-   支持 7 家保险公司的自动录单
-   处理国内、国际、跨境电商、单车责任险等多种保单类型
-   统一的数据格式转换和 API 调用
-   完整的错误处理和通知机制

### 1.3 支持的保险公司

| 公司代码 | 公司名称 | 特殊处理                         |
| -------- | -------- | -------------------------------- |
| PICC     | 人保财险 | 货物分类映射、保费强制计算       |
| CPIC     | 太保财险 | 地址格式、固定税号               |
| PINGAN   | 平安财险 | 复杂条款映射、制裁条款、倒签处理 |
| SINOSIG  | 阳光财险 | 运输方式代码、货物类别名称       |
| HUATAI   | 华泰财险 | 附加险逗号分隔                   |
| TPIC     | 太平财险 | JSON 协议配置、运输方式分类      |
| DIC      | 东海财险 | 简化配置                         |

## 2. 核心类和方法

### 2.1 PrepareSettings Trait

#### 2.1.1 主要方法

```php
public function getSetting($policy, $policyCargo, $product)
```

**功能**: 根据保险公司标识符获取对应的配置参数
**参数**:

-   `$policy`: 保单信息
-   `$policyCargo`: 货物信息
-   `$product`: 产品信息
    **返回**: 配置参数字符串（用|#|分隔）

```php
protected function getClauses($model)
```

**功能**: 获取保单或产品的条款信息
**逻辑**:

-   `parent_id == '-1'` 为主险
-   其他为附加险
    **返回**: `['main' => '主险代码', 'additional' => ['附加险代码']]`

```php
protected function getDefaultGoodsTypeSmall($companyIdentifier, $goodsType)
```

**功能**: 获取不同保司的货物小类映射
**支持保司**: PICC、PINGAN、CPIC

#### 2.1.2 字段整理方法

```php
protected function arrangeField($policy, $policyCargo)
```

**功能**: 国内险种字段整理
**特殊处理**:

-   预设数据覆盖逻辑
-   地址格式转换（除太保外，冒号转横杠）
-   人保保费强制计算

```php
public function arrangeFieldIntl($policy, $policyCargo)
```

**功能**: 国际险种字段整理
**特殊处理**:

-   制裁条款添加（平安）
-   币种汇率处理
-   保费币种配置
-   特殊字符清理

```php
protected function arrangeFieldLbt($policy, $policyCargo)
```

**功能**: 单车责任险字段整理
**特点**: 相对简化的字段处理

### 2.2 AutoInsure 类

#### 2.2.1 主要方法

```php
public function autopost($policy, $policyCargo, $product, $insureType = 'CARGO_CHN')
```

**功能**: 自动录单主入口
**流程**:

1. 环境检查（本地环境跳过）
2. 根据保单类型选择字段整理方法
3. 获取开票信息
4. 构建通知 URL
5. 获取免赔额和特别约定
6. 调用录单 API
7. 错误处理和通知

```php
public function send($messages, $settings, $product_code, $notifies, $policy)
```

**功能**: 发送录单请求到自动录单系统
**参数**:

-   `$messages`: JSON 格式的保单数据
-   `$settings`: JSON 格式的产品参数
-   `$product_code`: 产品代码
-   `$notifies`: 回调通知地址

## 3. 保险公司配置详解

### 3.1 人保财险 (PICC)

#### 3.1.1 配置结构

```php
protected function getPICCSettings($policy, $policyCargo, $product)
{
    $data = [
        $product['companyBranchAccount']['username'],     // 账号
        $product['companyBranchAccount']['password'],     // 密码
        $policyCargo['goodsType']['value'],               // 大类
        $this->getDefaultGoodsTypeSmall('PICC', $goodsType), // 小类
    ];

    // 国内险种特殊处理
    if ($product['type'] == Product::TYPE_DOMESTIC) {
        $data = array_merge($data, [
            $clauses['main'],                    // 主险
            $clauses['additional'][0] ?? 0,      // 盗抢险
        ]);
    }

    $data[] = $product['additional']['config']['agreement']; // 保单号头

    // 可选方案代码
    if (!empty($product['additional']['config']['plan'])) {
        $data[] = explode('-', $product['additional']['config']['plan'])[0];
    }

    return $data;
}
```

#### 3.1.2 货物分类映射

```php
$piccGoodsMapping = [
    '粮食、食品、果蔬、饮料、烟草及饲料' => '624',
    '轻纺、文体、日用品、工艺品' => '804',
    '医药类' => '884',
    '金属原料、矿产及建材类、玻璃及其制品' => '738',
    '其它' => '899',
    '木、木浆及木制品；纸、纸浆、纸板及其制品' => '880',
    '石油、化工原料及其制品' => '745',
    '电器、机械、运输工具、设备类' => '849',
];
```

#### 3.1.3 特殊业务逻辑

-   **保费计算**: 强制使用保司费率，不支持保费同步
-   **条款处理**: 国内险种支持主险+盗抢险，国际险种使用保单条款
-   **汇率处理**: 临时处理节假日汇率未更新问题（代码中已注释）

### 3.2 太保财险 (CPIC)

#### 3.2.1 配置结构

```php
protected function getCPICSettings($product)
{
    return [
        $product['companyBranchAccount']['username'],     // 账号
        $product['companyBranchAccount']['password'],     // 密码
        $product['additional']['config']['agreement']     // 协议ID
    ];
}
```

#### 3.2.2 特殊处理

-   **地址格式**: 保持冒号格式，不转换为横杠
-   **开票信息**: 使用固定税号 `91310230MA1K2FMP33`
-   **货物分类**: 详细的 17 类货物映射

### 3.3 平安财险 (PINGAN)

#### 3.3.1 配置结构

平安财险的配置最为复杂，支持新旧两套系统：

```php
protected function getPINGANSettings($policy, $product)
{
    $data = [
        $product['companyBranchAccount']['username'],     // 账号
        $product['companyBranchAccount']['password'],     // 密码
    ];

    if ($product['type'] == Product::TYPE_DOMESTIC) {
        if (($product['additional']['config']['is_new'] ?? '0') == '1') {
            // 新版本处理
            $planCode = explode('-', $product['additional']['config']['plan'])[0];
            $data = array_merge($data, [
                $product['additional']['config']['agreement'],
                explode('PL', $planCode)[0],      // 方案代码
                'PL' . explode('PL', $planCode)[1], // 险种代码
            ]);
        } else {
            // 旧版本处理
            $data = array_merge($data, [
                $product['additional']['config']['agreement'],
                $product['additional']['config']['plan'],    // 方案中文
                $clauseFlag[$clauses['main']],              // 主险中文
            ]);
        }
    }

    // 国际险种处理...

    return $data;
}
```

#### 3.3.2 条款映射

```php
$clauseFlag = [
    '*********' => '基本险',
    'PL0900025' => '综合险',
    'PL0900026' => '航空运输险',
    'PL0900027' => '航空运输一切险',
    'PL0900028' => '陆运险',
    'PL0900029' => '陆运一切险',
    'PL0900030' => '平安险',
    'PL0900031' => '水渍险',
    'PL0900032' => '一切险',
    'PL0900033' => '冷藏险',
    'PL0900034' => '冷藏一切险',
    'PL0900040' => 'INSTITUTE CARGO CLAUSES (A)1982',
    'PL0900041' => 'INSTITUTE CARGO CLAUSES (B)1982',
    'PL0900042' => 'INSTITUTE CARGO CLAUSES (C)1982',
    'PL0900044' => 'INSTITUTE CARGO CLAUSES (AIR)',
    'PL0900064' => '陆上运输冷藏货物保险',
];
```

#### 3.3.3 特殊业务逻辑

-   **新旧版本**: 通过 `is_new` 标识区分处理方式
-   **出口进口**: 协议和方案支持用 `|` 分隔的出口/进口配置
-   **制裁条款**: 自动添加制裁条款 `PL0900090`
-   **倒签处理**: 深圳、广东分公司特殊倒签保函处理

### 3.4 阳光财险 (SINOSIG)

#### 3.4.1 配置结构

```php
protected function getSIONSIGSettings($policy, $policyCargo, $product)
{
    $data = [
        $product['additional']['config']['agreement'],    // 协议
        $product['additional']['config']['plan'],         // 方案
        $policyCargo['transportMethod']['value'],         // 运输方式代码
        $policyCargo['goodsType']['value'],              // 货物类别代码
        $policyCargo['goodsType']['name'],               // 货物类别名称
        $clauses['main'],                                // 主险代码
    ];

    // 附加险处理
    if (!empty($clauses['additional'][0])) {
        array_merge($data, ['DQX']);  // 盗抢险固定代码
    }

    return $data;
}
```

#### 3.4.2 特殊处理

-   **货物类别**: 同时需要代码和名称
-   **运输方式**: 直接使用运输方式代码
-   **附加险**: 盗抢险使用固定代码 `DQX`

### 3.5 华泰财险 (HUATAI)

#### 3.5.1 配置结构

```php
protected function getHUATAISettings($policy, $product)
{
    $data = [
        $product['companyBranchAccount']['username'],     // 账号
        $product['additional']['config']['agreement'],    // 协议
        $product['additional']['config']['plan'],         // 方案
        $clauses['main']                                 // 险种代码
    ];

    // 附加险处理
    if (!empty($clauses['additional'][0])) {
        $data[] = implode(',', $clauses['additional']);  // 多个附加险用逗号分隔
    }

    return $data;
}
```

#### 3.5.2 特殊处理

-   **附加险**: 多个附加险用逗号分隔
-   **配置简洁**: 相对简化的配置结构

### 3.6 太平财险 (TPIC)

#### 3.6.1 配置结构

```php
protected function getTPICSettings($policy, $product)
{
    $account = $product['companyBranchAccount']['username'];
    $password = $product['companyBranchAccount']['password'];

    if ($product['type'] == Product::TYPE_DOMESTIC) {
        // 国内险种协议处理
        $agreementData = json_decode($product['additional']['config']['agreement'], true);
        if (empty($agreementData[$policy['policyCargo']['transportMethod']['value']])) {
            abort(400, '投保异常,请联系管理员处理');
        }
        $agreementData = $agreementData[$policy['policyCargo']['transportMethod']['value']];
        $agreement = explode('-', $agreementData)[0];
    } else {
        // 国际险种协议处理
        $agreementData = json_decode($product['additional']['config']['agreement'], true);
        $agreementData = $policy['policyCargo']['insure_type'] === 1
            ? $agreementData['EXPORT']
            : $agreementData['IMPORT'];
        if (empty($agreementData[$policy['policyCargo']['transportMethod']['value']])) {
            abort(400, '投保异常,请联系管理员处理');
        }
        $agreementData = $agreementData[$policy['policyCargo']['transportMethod']['value']];
        $agreement = explode('-', $agreementData)[0];
    }

    return [$account, $password, $agreement];
}
```

#### 3.6.2 协议配置结构

```json
// 国内险种
{
    "运输方式代码1": "协议号-描述",
    "运输方式代码2": "协议号-描述"
}

// 国际险种
{
    "EXPORT": {
        "运输方式代码1": "协议号-描述",
        "运输方式代码2": "协议号-描述"
    },
    "IMPORT": {
        "运输方式代码1": "协议号-描述",
        "运输方式代码2": "协议号-描述"
    }
}
```

#### 3.6.3 特殊处理

-   **JSON 配置**: 复杂的 JSON 结构配置协议
-   **运输方式**: 按运输方式分别配置协议
-   **进出口**: 国际险种按进出口分别配置
-   **错误处理**: 配置缺失时抛出异常
-   **地址格式**: 特殊的冒号分隔处理

### 3.7 东海财险 (DIC)

#### 3.7.1 配置结构

```php
protected function getDICSettings($policy, $policyCargo, $product)
{
    return [
        $product['additional']['config']['plan'],
        $policyCargo['goodsType']['value'] . '-' . $policyCargo['goodsType']['name']
    ];
}
```

#### 3.7.2 特殊处理

-   **配置简化**: 最简单的配置结构
-   **货物类型**: 代码-名称格式

## 4. 数据处理流程

### 4.1 主流程

```mermaid
graph TD
    A[开始] --> B[环境检查]
    B --> C{本地环境?}
    C -->|是| D[跳过录单]
    C -->|否| E[根据保单类型选择处理方法]
    E --> F{保单类型}
    F -->|国内| G[arrangeField]
    F -->|国际/跨境| H[arrangeFieldIntl]
    F -->|单车责任| I[arrangeFieldLbt]
    G --> J[获取开票信息]
    H --> J
    I --> J
    J --> K[构建通知URL]
    K --> L[获取免赔额和特别约定]
    L --> M[获取保司配置]
    M --> N[发送录单请求]
    N --> O{录单成功?}
    O -->|否| P[发送错误通知]
    O -->|是| Q[结束]
    P --> Q
```

### 4.2 字段整理流程

#### 4.2.1 预设数据处理

```php
if ($policy['user']['is_use_insure_preset_data']) {
    $presetData = json_decode($policy['user']['insure_preset_data'], true);

    // 投保人信息覆盖
    $policy['policyholder'] = !empty($presetData['policyholder'])
        ? $presetData['policyholder']
        : $policy['policyholder'];
    $policy['policyholder_type'] = !empty($presetData['policyholder_type'])
        ? $presetData['policyholder_type']
        : $policy['policyholder_type'];
    // ... 其他字段类似处理

    // 被保人信息覆盖
    $policy['insured'] = !empty($presetData['insured'])
        ? $presetData['insured']
        : $policy['insured'];
    // ... 其他字段类似处理
}
```

#### 4.2.2 地址格式处理

```php
$fromLoc = $policyCargo['departure'];
$viaLoc = $policyCargo['transmit'];
$toLoc = $policyCargo['destination'];

// 太保保持冒号格式，其他保司转换为横杠
if ($policy['company']['identifier'] != 'CPIC') {
    $fromLoc = str_replace(':', '-', $policyCargo['departure']);
    $viaLoc = str_replace(':', '-', $policyCargo['transmit']);
    $toLoc = str_replace(':', '-', $policyCargo['destination']);
}
```

#### 4.2.3 保费计算逻辑

```php
// 保费同步处理
if ($policy['is_premium_sync'] == 0) {
    $data['premium'] = $policy['premium'];
    $data['ratio'] = $policy['rate'];
}

// 人保特殊处理：强制使用保司费率
if ($policy['company']['identifier'] === 'PICC') {
    $data['premium'] = $this->calcPremium(
        $policy['coverage'],
        $policy['rate'],
        $policy['minimum_premium']
    );
    $data['ratio'] = $policy['rate'];
}

// 保费计算公式
protected function calcPremium(int $coverage, float $rate, int $minimum = 0)
{
    $premium = bcmul($coverage, bcdiv($rate, 10000, 6), 5);
    return round($premium < $minimum ? $minimum : $premium, 2);
}
```

### 4.3 国际险种特殊处理

#### 4.3.1 制裁条款处理

```php
// 平安财险制裁条款自动添加
if ($policy['company']['identifier'] == 'PINGAN' && $this->isSanctionists($policy['product'], $policy)) {
    $clauses['additional'][] = 'PL0900090';
}

// 制裁国家判断
protected function isSanctionists($product, $policy)
{
    $isSanctionists = explode('|', $product['additional']['sanctionist']);
    $departure = in_array($product['type'], [Product::TYPE_INTL, Product::TYPE_CBEC])
        ? $policy['policyCargo']['departure']
        : explode('-', $policy['policyCargo']['departure'])[0];
    $destination = in_array($product['type'], [Product::TYPE_INTL, Product::TYPE_CBEC])
        ? $policy['policyCargo']['destination']
        : explode('-', $policy['policyCargo']['destination'])[0];

    return in_array($departure, $isSanctionists) || in_array($destination, $isSanctionists);
}
```

#### 4.3.2 特殊字符处理

```php
// 清理特殊字符
foreach ($policy as &$val) {
    $val = str_replace(["\r\n", "\r", "\n", "\t"], '', $val);
}
foreach ($policyCargo as &$value) {
    $value = str_replace(["\r\n", "\r", "\n", "\t"], '', $value);
}
```

#### 4.3.3 币种处理

```php
$currency = Currency::find($policyCargo['coverage_currency_id']);
$currencyRate = $currency['rate'];

// 非标准币种处理
if (!in_array($policyCargo['invoiceCurrency']['code'], ['HKD', 'CNY', 'USD'])) {
    $data['usdExchange'] = Currency::where('code', 'USD')->orderBy('id', 'desc')->first()['rate'];
    $data['usdPremium'] = bcdiv(
        round(bcdiv(
            $policy['is_premium_sync'] == 1 ? $policy['user_premium'] : $policy['premium'],
            $data['usdExchange'],
            5
        ), 3),
        100,
        2
    );
}

// 保费币种配置
if (($policy['product']['additional']['config']['premium_currency'] ?? '') == 'BY_AMOUNT') {
    $data['premiumCurrency'] = $data['invCurrency'];
}
```

## 5. 特殊业务逻辑

### 5.1 倒签处理

#### 5.1.1 倒签判断

```php
protected function isBackdating($policy, $product)
{
    $antiDatedDays = ($product['additional']['anti_dated_days'] ?? 0);
    $submittedAt = Carbon::parse($policy['submitted_at']);
    $shippingDate = Carbon::parse($policy['policyCargo']['shipping_date']);

    if ($submittedAt->startOfDay()->gt($shippingDate->addDays($antiDatedDays)->startOfDay())) {
        return true;
    }
    return false;
}
```

#### 5.1.2 倒签保函处理

```php
protected function handleAntiDatedFile($policy, $data)
{
    // 基础倒签保函处理
    $data['antiDatedFileName'] = $policy['policyCargo']['anti_dated_file']
        ? '倒签保函.' . pathinfo($policy['policyCargo']['anti_dated_file'], PATHINFO_EXTENSION)
        : '';
    $data['antiDatedFile'] = $policy['policyCargo']['anti_dated_file']
        && Storage::disk('public')->exists($policy['policyCargo']['anti_dated_file'])
        ? base64_encode(Storage::disk('public')->get($policy['policyCargo']['anti_dated_file']))
        : '';

    // 平安深圳、平安广东特殊处理
    if (in_array($policy['company_branch_id'], [20, 29])) {
        if (date('Y-m-d', strtotime($policy['policyCargo']['shipping_date'])) < date('Y-m-d')
            && !$this->isBackdating($policy, $policy['product'])) {
            $data['antiDatedFileName'] = '_倒签保函.png';
            $data['antiDatedFile'] = base64_encode(
                file_get_contents(resource_path('static/img/anti_dated_file.png'))
            );
        }
    }

    return $data;
}
```

### 5.2 保障到港处理

```php
protected function isOnlyPortRegions($policy)
{
    $onlyPortRegions = explode('|', $policy['product']['additional']['only_port_regions']);

    // 根据保单类型和保司获取起运地和目的地
    $departure = in_array($policy['type'], [Policy::TYPE_INTL, Policy::TYPE_CBEC])
        ? ($policy['company']['identifier'] == 'TPIC'
            ? explode(':', $policy['policyCargo']['departure'])[0]
            : $policy['policyCargo']['departure'])
        : explode('-', $policy['policyCargo']['departure'])[0];

    $destination = in_array($policy['type'], [Policy::TYPE_INTL, Policy::TYPE_CBEC])
        ? ($policy['company']['identifier'] == 'TPIC'
            ? explode(':', $policy['policyCargo']['destination'])[0]
            : $policy['policyCargo']['destination'])
        : explode('-', $policy['policyCargo']['destination'])[0];

    if (in_array($departure, $onlyPortRegions) || in_array($destination, $onlyPortRegions)) {
        // 检查用户是否配置了免港到港条款
        $subjectClause = UserSubjectClause::where('user_id', $policy['user_id'])
            ->where('subject_id', $policy['policyCargo']['subject_id'])
            ->whereHas('companyBranches', fn($q) => $q->where('company_branch_id', $policy['company_branch_id']))
            ->where('product_type', $policy['type'])
            ->first();

        if ($subjectClause && $subjectClause['without_port_to_port_clause']) {
            return 0;
        }

        return 1;
    }

    return 0;
}
```

### 5.3 投保类型处理

```php
protected function getInsureType($insureType)
{
    $types = [
        '1' => 'export',   // 出口
        '2' => 'import',   // 进口
        '3' => 'overseas', // 境外
    ];

    return $types[$insureType];
}
```

### 5.4 开票信息处理

```php
protected function getInvoiceData($data, $policy, $user)
{
    if (!is_null($user['invoice'])) {
        // 企业开票
        $data['invHead'] = $user['invoice']['company_name'];
        $data['invTaxNo'] = $user['invoice']['tax_no'];
    } else {
        // 个人开票
        $data['invHead'] = $user['name'];
        $data['invTaxNo'] = $user['idcard_no'];

        // 太保特殊处理：使用固定税号
        if ($policy['company']['identifier'] == 'CPIC') {
            $data['invTaxNo'] = '91310230MA1K2FMP33';
        }
    }

    return $data;
}
```

## 6. 错误处理机制

### 6.1 录单失败处理

```php
public function autopost($policy, $policyCargo, $product, $insureType = 'CARGO_CHN')
{
    // ... 录单逻辑 ...

    $callback = $this->send($messages, $settings, $product['additional']['config']['product_code'], $notifies, $policy);

    // 错误检查和通知
    if (Str::contains($callback, 'error')) {
        $this->sendWeChatMessage($policy['order_no']);  // 企业微信通知
        $this->sendMailMessage($policy);                // 邮件通知
    }

    return $callback;
}
```

### 6.2 企业微信通知

```php
protected function sendWeChatMessage($orderNo)
{
    $message = $this->buildWeComMessage($orderNo);
    $config = config('baoya.capture_exception.wecom');
    $weCom = new WeCom($config);
    $weCom->send($message);
}

protected function buildWeComMessage($orderNo)
{
    return <<<Markdown
    自动录单提交失败通知：
    >**详情**
    >流水号：{$orderNo}
    >错误信息: 请检查单票信息是否存在特殊字符
    Markdown;
}
```

### 6.3 邮件通知

```php
protected function sendMailMessage($policy)
{
    $message = '自动录单提交失败提醒 流水号:' . $policy['order_no'];
    $emails = [
        '<EMAIL>',
        '<EMAIL>',
    ];

    // 内部邮箱通知
    Mail::raw($message, function ($mail) use ($message, $emails) {
        $mail->to($emails)->subject($message);
    });

    // 保司邮箱通知
    if (isset($policy['product']['companyBranchAccount']['config']['report_email_recipients'])) {
        $reportEmails = explode(';', $policy['product']['companyBranchAccount']['config']['report_email_recipients']);
        $reportEmails = array_merge($emails, $reportEmails);

        Notification::route('mail', $reportEmails)
            ->notify(new PolicyInsureSubmitFailNotifiication($policy));
    }
}
```

### 6.4 配置缺失处理

```php
// 太平财险配置检查
if (empty($agreementData[$policy['policyCargo']['transportMethod']['value']])) {
    abort(400, '投保异常,请联系管理员处理');
}
```

### 6.5 环境检查

```php
// 本地环境跳过录单
if (app()->environment('local', 'development', 'staging', 'testing')) {
    return;
}
```

## 7. 重构建议

### 7.1 架构重构

#### 7.1.1 策略模式重构

```php
// 保司处理接口
interface InsuranceCompanyHandler
{
    public function getSettings($policy, $policyCargo, $product): array;
    public function arrangeFields($policy, $policyCargo): array;
    public function validateConfig($product): bool;
}

// 具体保司实现
class PICCHandler implements InsuranceCompanyHandler
{
    public function getSettings($policy, $policyCargo, $product): array
    {
        // PICC特定逻辑
    }

    public function arrangeFields($policy, $policyCargo): array
    {
        // PICC字段整理逻辑
    }

    public function validateConfig($product): bool
    {
        // PICC配置验证
    }
}

// 工厂类
class InsuranceHandlerFactory
{
    public static function create(string $companyIdentifier): InsuranceCompanyHandler
    {
        switch ($companyIdentifier) {
            case 'PICC':
                return new PICCHandler();
            case 'CPIC':
                return new CPICHandler();
            // ... 其他保司
            default:
                throw new InvalidArgumentException("Unsupported company: {$companyIdentifier}");
        }
    }
}
```

#### 7.1.2 配置外置

```php
// config/insurance_companies.php
return [
    'PICC' => [
        'goods_type_mapping' => [
            '粮食、食品、果蔬、饮料、烟草及饲料' => '624',
            // ... 其他映射
        ],
        'clause_mapping' => [
            // 条款映射
        ],
        'special_rules' => [
            'force_company_premium' => true,
            'support_theft_insurance' => true,
        ]
    ],
    'PINGAN' => [
        'clause_mapping' => [
            '*********' => '基本险',
            // ... 其他映射
        ],
        'special_rules' => [
            'support_new_version' => true,
            'support_sanction_clause' => true,
            'special_branches' => [20, 29], // 深圳、广东
        ]
    ],
    // ... 其他保司配置
];
```

#### 7.1.3 数据传输对象(DTO)

```php
class PolicyData
{
    public string $orderNo;
    public string $companyIdentifier;
    public int $type;
    public array $policyholder;
    public array $insured;
    public array $cargo;
    // ... 其他字段

    public static function fromArray(array $data): self
    {
        // 数组转DTO逻辑
    }
}

class InsuranceSettings
{
    public string $username;
    public string $password;
    public string $agreement;
    public ?string $plan;
    public array $clauses;
    // ... 其他配置
}
```

### 7.2 代码质量改进

#### 7.2.1 单一职责原则

-   将字段整理逻辑拆分为独立的服务类
-   将保司配置逻辑独立为配置管理器
-   将通知逻辑独立为通知服务

#### 7.2.2 依赖注入

```php
class AutoInsure
{
    public function __construct(
        private InsuranceHandlerFactory $handlerFactory,
        private NotificationService $notificationService,
        private ConfigurationManager $configManager
    ) {}
}
```

#### 7.2.3 异常处理改进

```php
class InsuranceException extends Exception
{
    public function __construct(
        string $message,
        public readonly string $orderNo,
        public readonly string $companyIdentifier,
        int $code = 0,
        ?Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
    }
}

class ConfigurationMissingException extends InsuranceException {}
class InvalidTransportMethodException extends InsuranceException {}
```

#### 7.2.4 测试覆盖

```php
class PICCHandlerTest extends TestCase
{
    public function test_picc_settings_generation()
    {
        // 测试PICC配置生成
    }

    public function test_picc_goods_type_mapping()
    {
        // 测试货物类型映射
    }

    public function test_picc_premium_calculation()
    {
        // 测试保费计算
    }
}
```

### 7.3 性能优化

#### 7.3.1 缓存机制

```php
class CachedConfigurationManager
{
    public function getGoodsTypeMapping(string $companyIdentifier): array
    {
        return Cache::remember(
            "goods_type_mapping_{$companyIdentifier}",
            3600,
            fn() => $this->loadGoodsTypeMapping($companyIdentifier)
        );
    }
}
```

#### 7.3.2 数据库查询优化

```php
// 预加载相关数据
$policy = Policy::with([
    'company',
    'product.companyBranchAccount',
    'product.additional',
    'policyCargo.goodsType',
    'policyCargo.transportMethod',
    'user.invoice'
])->find($policyId);
```

### 7.4 监控和日志

#### 7.4.1 业务日志

```php
class InsuranceLogger
{
    public function logInsuranceAttempt(string $orderNo, string $company): void
    {
        Log::info('Insurance attempt started', [
            'order_no' => $orderNo,
            'company' => $company,
            'timestamp' => now()
        ]);
    }

    public function logInsuranceSuccess(string $orderNo, array $response): void
    {
        Log::info('Insurance completed successfully', [
            'order_no' => $orderNo,
            'response' => $response
        ]);
    }

    public function logInsuranceFailure(string $orderNo, string $error): void
    {
        Log::error('Insurance failed', [
            'order_no' => $orderNo,
            'error' => $error
        ]);
    }
}
```

#### 7.4.2 性能监控

```php
class PerformanceMonitor
{
    public function measureInsuranceTime(callable $callback, string $orderNo)
    {
        $startTime = microtime(true);

        try {
            $result = $callback();
            $this->recordSuccess($orderNo, microtime(true) - $startTime);
            return $result;
        } catch (Exception $e) {
            $this->recordFailure($orderNo, microtime(true) - $startTime, $e);
            throw $e;
        }
    }
}
```

## 8. 总结

### 8.1 当前系统特点

-   **功能完整**: 支持 7 家保司的自动录单
-   **业务复杂**: 每家保司都有特殊的处理逻辑
-   **耦合度高**: 所有逻辑集中在两个文件中
-   **维护困难**: 新增保司或修改逻辑影响面大

### 8.2 重构收益

-   **可维护性**: 每家保司独立维护，互不影响
-   **可扩展性**: 新增保司只需实现接口
-   **可测试性**: 独立的类便于单元测试
-   **可读性**: 清晰的职责分离和命名规范

### 8.3 实施建议

1. **分阶段重构**: 先重构一家保司，验证架构可行性
2. **保持兼容**: 重构过程中保持原有接口不变
3. **充分测试**: 每个阶段都要有完整的测试覆盖
4. **文档同步**: 及时更新技术文档和业务文档
5. **团队培训**: 确保团队理解新的架构设计

这份详细的分析文档为系统重构提供了全面的参考，包含了所有业务逻辑的细节和具体的重构建议。
